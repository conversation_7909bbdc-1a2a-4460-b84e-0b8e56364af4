"use strict";
const common_vendor = require("../../common/vendor.js");
const src_stores_modules_user = require("../../src/stores/modules/user.js");
const src_api_modules_user = require("../../src/api/modules/user.js");
if (!Array) {
  const _easycom_u_input2 = common_vendor.resolveComponent("u-input");
  const _easycom_u_form_item2 = common_vendor.resolveComponent("u-form-item");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_form2 = common_vendor.resolveComponent("u-form");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_input2 + _easycom_u_form_item2 + _easycom_u_icon2 + _easycom_u_form2 + _easycom_u_button2)();
}
const _easycom_u_input = () => "../../uni_modules/uview-plus/components/u-input/u-input.js";
const _easycom_u_form_item = () => "../../uni_modules/uview-plus/components/u-form-item/u-form-item.js";
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_form = () => "../../uni_modules/uview-plus/components/u-form/u-form.js";
const _easycom_u_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_input + _easycom_u_form_item + _easycom_u_icon + _easycom_u_form + _easycom_u_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "register",
  setup(__props) {
    const userStore = src_stores_modules_user.useUserStore();
    common_vendor.storeToRefs(userStore);
    const { updateProfile } = userStore;
    const formRef = common_vendor.ref();
    const isSubmitting = common_vendor.ref(false);
    const institutions = common_vendor.ref([]);
    const positions = common_vendor.ref([]);
    const institutionDisplayName = common_vendor.ref("");
    const positionDisplayName = common_vendor.ref("");
    const updateKey = common_vendor.ref(0);
    const formData = common_vendor.reactive({
      realName: "",
      phone: "",
      idCard: "",
      institutionId: "",
      positionId: "",
      avatar: ""
    });
    const formRules = common_vendor.reactive({
      realName: [
        { required: true, message: "请输入真实姓名", trigger: "blur" },
        { min: 2, max: 20, message: "姓名长度在2-20个字符", trigger: "blur" }
      ],
      phone: [
        { required: true, message: "请输入手机号码", trigger: "blur" },
        { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
      ],
      idCard: [
        { required: true, message: "请输入身份证号码", trigger: "blur" },
        { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: "请输入正确的身份证号码", trigger: "blur" }
      ],
      institutionId: [
        { required: true, message: "请选择隶属机构", trigger: "change" }
      ],
      positionId: [
        { required: true, message: "请选择职位", trigger: "change" }
      ],
      avatar: [
        { required: true, message: "请上传本人照片", trigger: "change" }
      ]
    });
    common_vendor.onMounted(() => {
      loadInitialData();
    });
    async function loadInitialData() {
      try {
        common_vendor.index.showLoading({ title: "加载中..." });
        common_vendor.index.__f__("log", "at pages/register/register.vue:201", "开始加载机构和职位数据...");
        const [institutionsData, positionsData] = await Promise.all([
          src_api_modules_user.getInstitutions(),
          src_api_modules_user.getPositions()
        ]);
        common_vendor.index.__f__("log", "at pages/register/register.vue:208", "机构数据:", institutionsData);
        common_vendor.index.__f__("log", "at pages/register/register.vue:209", "职位数据:", positionsData);
        if (Array.isArray(institutionsData)) {
          institutions.value = institutionsData;
          common_vendor.index.__f__("log", "at pages/register/register.vue:214", "机构数据设置成功，数量:", institutions.value.length);
        } else {
          common_vendor.index.__f__("error", "at pages/register/register.vue:216", "机构数据不是数组格式:", institutionsData);
          institutions.value = [];
        }
        if (Array.isArray(positionsData)) {
          positions.value = positionsData;
          common_vendor.index.__f__("log", "at pages/register/register.vue:222", "职位数据设置成功，数量:", positions.value.length);
        } else {
          common_vendor.index.__f__("error", "at pages/register/register.vue:224", "职位数据不是数组格式:", positionsData);
          positions.value = [];
        }
        common_vendor.index.hideLoading();
      } catch (error) {
        common_vendor.index.hideLoading();
        common_vendor.index.__f__("error", "at pages/register/register.vue:231", "加载机构和职位数据失败:", error);
        institutions.value = [];
        positions.value = [];
        common_vendor.index.showToast({
          title: "数据加载失败，请重试",
          icon: "none"
        });
      }
    }
    function selectInstitution() {
      if (!Array.isArray(institutions.value) || institutions.value.length === 0) {
        common_vendor.index.showToast({
          title: "机构数据尚未加载",
          icon: "none"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages/register/register.vue:256", "显示机构选择器，可选机构:", institutions.value);
      common_vendor.index.showActionSheet({
        itemList: institutions.value.map((item) => item.name),
        success: (res) => {
          const selectedInstitution = institutions.value[res.tapIndex];
          formData.institutionId = selectedInstitution.id;
          institutionDisplayName.value = selectedInstitution.name;
          updateKey.value++;
          common_vendor.index.__f__("log", "at pages/register/register.vue:265", "选择了机构:", selectedInstitution);
        }
      });
    }
    function selectPosition() {
      if (!Array.isArray(positions.value) || positions.value.length === 0) {
        common_vendor.index.showToast({
          title: "职位数据尚未加载",
          icon: "none"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages/register/register.vue:282", "显示职位选择器，可选职位:", positions.value);
      common_vendor.index.showActionSheet({
        itemList: positions.value.map((item) => item.name),
        success: (res) => {
          const selectedPosition = positions.value[res.tapIndex];
          formData.positionId = selectedPosition.id;
          positionDisplayName.value = selectedPosition.name;
          updateKey.value++;
          common_vendor.index.__f__("log", "at pages/register/register.vue:291", "选择了职位:", selectedPosition);
        }
      });
    }
    function checkFileSize(filePath) {
      return new Promise((resolve) => {
        common_vendor.index.getFileInfo({
          filePath,
          success: (res) => {
            const maxSize = 200 * 1024;
            common_vendor.index.__f__("log", "at pages/register/register.vue:307", "文件大小:", res.size, "字节，限制:", maxSize, "字节");
            resolve(res.size <= maxSize);
          },
          fail: () => {
            common_vendor.index.__f__("error", "at pages/register/register.vue:311", "获取文件信息失败");
            resolve(false);
          }
        });
      });
    }
    function uploadAvatar() {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["camera", "album"],
        success: async (res) => {
          try {
            const filePath = res.tempFilePaths[0];
            const isValidSize = await checkFileSize(filePath);
            if (!isValidSize) {
              common_vendor.index.showToast({
                title: "照片大小超过200KB，请重新选择或压缩后上传",
                icon: "none",
                duration: 3e3
              });
              return;
            }
            common_vendor.index.showLoading({ title: "上传中..." });
            formData.avatar = filePath;
            common_vendor.index.hideLoading();
            common_vendor.index.showToast({
              title: "上传成功",
              icon: "success"
            });
          } catch (error) {
            common_vendor.index.hideLoading();
            common_vendor.index.__f__("error", "at pages/register/register.vue:353", "上传失败:", error);
            common_vendor.index.showToast({
              title: "上传失败，请重试",
              icon: "none"
            });
          }
        }
      });
    }
    async function submitForm() {
      try {
        await formRef.value.validate();
        isSubmitting.value = true;
        await src_api_modules_user.submitUserInfo(formData);
        updateProfile({ status: "pending_review" });
        common_vendor.index.showToast({
          title: "提交成功，请等待审核",
          icon: "success",
          duration: 2e3
        });
        setTimeout(() => {
          common_vendor.index.reLaunch({ url: "/pages/study/study" });
        }, 2e3);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/register/register.vue:391", "提交失败:", error);
        common_vendor.index.showToast({
          title: "提交失败，请重试",
          icon: "none"
        });
      } finally {
        isSubmitting.value = false;
      }
    }
    function skipRegister() {
      common_vendor.index.showModal({
        title: "确认跳过",
        content: "跳过后您将无法参加考试，只能进行学习练习，确认跳过吗？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.reLaunch({ url: "/pages/study/study" });
          }
        }
      });
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(($event) => formData.realName = $event),
        b: common_vendor.p({
          placeholder: "请输入真实姓名",
          clearable: true,
          maxlength: "20",
          modelValue: formData.realName
        }),
        c: common_vendor.p({
          label: "真实姓名",
          prop: "realName",
          required: true
        }),
        d: common_vendor.o(($event) => formData.phone = $event),
        e: common_vendor.p({
          placeholder: "请输入手机号码",
          type: "number",
          clearable: true,
          maxlength: "11",
          modelValue: formData.phone
        }),
        f: common_vendor.p({
          label: "联系电话",
          prop: "phone",
          required: true
        }),
        g: common_vendor.o(($event) => formData.idCard = $event),
        h: common_vendor.p({
          placeholder: "请输入身份证号码",
          clearable: true,
          maxlength: "18",
          modelValue: formData.idCard
        }),
        i: common_vendor.p({
          label: "身份证号码",
          prop: "idCard",
          required: true
        }),
        j: `institution-${updateKey.value}`,
        k: common_vendor.o(($event) => institutionDisplayName.value = $event),
        l: common_vendor.p({
          placeholder: "请选择所属机构",
          readonly: true,
          suffixIcon: "arrow-down",
          modelValue: institutionDisplayName.value
        }),
        m: common_vendor.o(selectInstitution),
        n: common_vendor.p({
          label: "隶属机构",
          prop: "institutionId",
          required: true
        }),
        o: `position-${updateKey.value}`,
        p: common_vendor.o(($event) => positionDisplayName.value = $event),
        q: common_vendor.p({
          placeholder: "请选择职位",
          readonly: true,
          suffixIcon: "arrow-down",
          modelValue: positionDisplayName.value
        }),
        r: common_vendor.o(selectPosition),
        s: common_vendor.p({
          label: "职位",
          prop: "positionId",
          required: true
        }),
        t: !formData.avatar
      }, !formData.avatar ? {
        v: common_vendor.p({
          name: "camera",
          size: "60",
          color: "#cccccc"
        }),
        w: common_vendor.o(uploadAvatar)
      } : {
        x: formData.avatar,
        y: common_vendor.p({
          name: "camera",
          size: "40",
          color: "#ffffff"
        }),
        z: common_vendor.o(uploadAvatar)
      }, {
        A: common_vendor.p({
          label: "本人照片",
          prop: "avatar",
          required: true
        }),
        B: common_vendor.sr(formRef, "bac4a35d-0", {
          "k": "formRef"
        }),
        C: common_vendor.p({
          model: formData,
          rules: formRules,
          labelPosition: "top",
          labelWidth: "auto"
        }),
        D: common_vendor.o(submitForm),
        E: common_vendor.p({
          type: "primary",
          loading: isSubmitting.value,
          loadingText: "提交中...",
          customStyle: "width: 100%; margin-bottom: 24rpx;"
        }),
        F: common_vendor.o(skipRegister),
        G: common_vendor.p({
          type: "info",
          plain: true,
          customStyle: "width: 100%;"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-bac4a35d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/register/register.js.map
