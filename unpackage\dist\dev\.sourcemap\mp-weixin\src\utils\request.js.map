{"version": 3, "file": "request.js", "sources": ["src/utils/request.ts"], "sourcesContent": ["/**\n * HTTP请求封装\n * 基于luch-request的统一请求封装\n * 更新时间：2025-06-20T11:00:39\n */\nimport Request from 'luch-request';\nimport { useUserStore } from '../stores/modules/user';\nimport type { ApiResponse } from '../types/api';\n\n// 创建请求实例\nconst http = new Request({\n  baseURL: 'http://127.0.0.1:3000/api/v1', // TODO: 替换为实际的API地址\n  timeout: 10000,\n  header: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 请求拦截器\nhttp.interceptors.request.use(\n  (config) => {\n    // 显示加载提示\n    uni.showLoading({\n      title: '加载中...',\n      mask: true,\n    });\n\n    // 添加token\n    const userStore = useUserStore();\n    if (userStore.token) {\n      config.header.Authorization = `Bearer ${userStore.token}`;\n    }\n\n    return config;\n  },\n  (error) => {\n    uni.hideLoading();\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\nhttp.interceptors.response.use(\n  (response) => {\n    uni.hideLoading();\n\n    // 统一以 HTTP statusCode 为判断依据\n    const { statusCode, data } = response as {\n      statusCode: number;\n      data: ApiResponse<any>;\n    };\n\n    // HTTP 状态码检查\n    if (statusCode < 200 || statusCode >= 400) {\n      // Token 失效\n      if (statusCode === 401) {\n        const userStore = useUserStore();\n        userStore.clearProfile();\n\n        uni.showToast({\n          title: '登录已过期，请重新登录',\n          icon: 'none',\n          duration: 2000,\n        });\n\n        // 跳转到登录页\n        setTimeout(() => {\n          uni.reLaunch({\n            url: '/pages/login/login',\n          });\n        }, 2000);\n\n        return Promise.reject(data);\n      }\n\n      // 其他HTTP错误\n      uni.showToast({\n        title: data?.message || '请求失败',\n        icon: 'none',\n        duration: 2000,\n      });\n\n      return Promise.reject(data);\n    }\n\n    // HTTP 2xx/3xx 成功，检查业务状态码\n    if (data && typeof data === 'object' && 'code' in data) {\n      // 业务成功\n      if (data.code === 200) {\n        console.log('API响应成功，返回数据:', data.data);\n        return Promise.resolve(data.data);\n      }\n\n      // 业务失败\n      console.error('API业务失败:', data);\n      uni.showToast({\n        title: data.message || '操作失败',\n        icon: 'none',\n        duration: 2000,\n      });\n\n      return Promise.reject(data);\n    }\n\n    // 兼容性处理：如果响应不是标准包裹结构，直接返回\n    // 注意：这种情况通常不应该发生，如果频繁出现需要检查API实现\n    console.warn('响应数据不符合标准包裹结构，直接返回原始数据:', data);\n    return Promise.resolve(data);\n  },\n  (error) => {\n    uni.hideLoading();\n    \n    console.error('HTTP请求错误:', error);\n    \n    // 网络错误处理\n    let errorMessage = '网络错误，请稍后重试';\n    \n    if (error.statusCode) {\n      switch (error.statusCode) {\n        case 404:\n          errorMessage = '请求的资源不存在';\n          break;\n        case 500:\n          errorMessage = '服务器内部错误';\n          break;\n        case 502:\n          errorMessage = '网关错误';\n          break;\n        case 503:\n          errorMessage = '服务不可用';\n          break;\n        case 504:\n          errorMessage = '网关超时';\n          break;\n        default:\n          errorMessage = `请求失败 (${error.statusCode})`;\n      }\n    }\n    \n    uni.showToast({\n      title: errorMessage,\n      icon: 'none',\n      duration: 2000,\n    });\n    \n    return Promise.reject(error);\n  }\n);\n\nexport default http;\n"], "names": ["Request", "uni", "useUserStore"], "mappings": ";;;AAUM,MAAA,OAAO,IAAIA,cAAAA,QAAQ;AAAA,EACvB,SAAS;AAAA;AAAA,EACT,SAAS;AAAA,EACT,QAAQ;AAAA,IACN,gBAAgB;AAAA,EAClB;AACF,CAAC;AAGD,KAAK,aAAa,QAAQ;AAAA,EACxB,CAAC,WAAW;AAEVC,kBAAAA,MAAI,YAAY;AAAA,MACd,OAAO;AAAA,MACP,MAAM;AAAA,IAAA,CACP;AAGD,UAAM,YAAYC,wBAAAA;AAClB,QAAI,UAAU,OAAO;AACnB,aAAO,OAAO,gBAAgB,UAAU,UAAU,KAAK;AAAA,IACzD;AAEO,WAAA;AAAA,EACT;AAAA,EACA,CAAC,UAAU;AACTD,kBAAA,MAAI,YAAY;AACT,WAAA,QAAQ,OAAO,KAAK;AAAA,EAC7B;AACF;AAGA,KAAK,aAAa,SAAS;AAAA,EACzB,CAAC,aAAa;AACZA,kBAAA,MAAI,YAAY;AAGV,UAAA,EAAE,YAAY,KAAS,IAAA;AAMzB,QAAA,aAAa,OAAO,cAAc,KAAK;AAEzC,UAAI,eAAe,KAAK;AACtB,cAAM,YAAYC,wBAAAA;AAClB,kBAAU,aAAa;AAEvBD,sBAAAA,MAAI,UAAU;AAAA,UACZ,OAAO;AAAA,UACP,MAAM;AAAA,UACN,UAAU;AAAA,QAAA,CACX;AAGD,mBAAW,MAAM;AACfA,wBAAAA,MAAI,SAAS;AAAA,YACX,KAAK;AAAA,UAAA,CACN;AAAA,WACA,GAAI;AAEA,eAAA,QAAQ,OAAO,IAAI;AAAA,MAC5B;AAGAA,oBAAAA,MAAI,UAAU;AAAA,QACZ,QAAO,6BAAM,YAAW;AAAA,QACxB,MAAM;AAAA,QACN,UAAU;AAAA,MAAA,CACX;AAEM,aAAA,QAAQ,OAAO,IAAI;AAAA,IAC5B;AAGA,QAAI,QAAQ,OAAO,SAAS,YAAY,UAAU,MAAM;AAElD,UAAA,KAAK,SAAS,KAAK;AACrBA,sBAAA,MAAY,MAAA,OAAA,8BAAA,iBAAiB,KAAK,IAAI;AAC/B,eAAA,QAAQ,QAAQ,KAAK,IAAI;AAAA,MAClC;AAGAA,oBAAA,MAAc,MAAA,SAAA,8BAAA,YAAY,IAAI;AAC9BA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,KAAK,WAAW;AAAA,QACvB,MAAM;AAAA,QACN,UAAU;AAAA,MAAA,CACX;AAEM,aAAA,QAAQ,OAAO,IAAI;AAAA,IAC5B;AAIAA,kBAAA,MAAa,MAAA,QAAA,+BAAA,2BAA2B,IAAI;AACrC,WAAA,QAAQ,QAAQ,IAAI;AAAA,EAC7B;AAAA,EACA,CAAC,UAAU;AACTA,kBAAA,MAAI,YAAY;AAEhBA,kBAAA,MAAA,MAAA,SAAA,+BAAc,aAAa,KAAK;AAGhC,QAAI,eAAe;AAEnB,QAAI,MAAM,YAAY;AACpB,cAAQ,MAAM,YAAY;AAAA,QACxB,KAAK;AACY,yBAAA;AACf;AAAA,QACF,KAAK;AACY,yBAAA;AACf;AAAA,QACF,KAAK;AACY,yBAAA;AACf;AAAA,QACF,KAAK;AACY,yBAAA;AACf;AAAA,QACF,KAAK;AACY,yBAAA;AACf;AAAA,QACF;AACiB,yBAAA,SAAS,MAAM,UAAU;AAAA,MAC5C;AAAA,IACF;AAEAA,kBAAAA,MAAI,UAAU;AAAA,MACZ,OAAO;AAAA,MACP,MAAM;AAAA,MACN,UAAU;AAAA,IAAA,CACX;AAEM,WAAA,QAAQ,OAAO,KAAK;AAAA,EAC7B;AACF;;"}