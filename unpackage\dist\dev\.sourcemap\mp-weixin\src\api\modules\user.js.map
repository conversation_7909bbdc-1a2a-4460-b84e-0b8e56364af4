{"version": 3, "file": "user.js", "sources": ["src/api/modules/user.ts"], "sourcesContent": ["/**\n * 用户相关API接口\n * 更新时间：2025-06-20T11:00:39\n */\nimport http from '../../utils/request';\nimport type { UserInfo, LoginParams, RegisterParams, WxLoginData, Institution, Position } from '../../types/api';\n\n/**\n * 微信登录 - 使用统一包裹结构处理\n */\nexport function wxLogin(params: LoginParams) {\n  return http.post<WxLoginData>('/auth/wechat-login', params);\n}\n\n/**\n * 获取机构列表\n */\nexport function getInstitutions() {\n  return http.get<Institution[]>('/institutions');\n}\n\n/**\n * 获取职位列表\n */\nexport function getPositions() {\n  return http.get<Position[]>('/positions');\n}\n\n/**\n * 提交用户注册信息\n */\nexport function submitUserInfo(params: RegisterParams) {\n  return http.post<boolean>('/profile', params);\n}\n\n/**\n * 获取用户信息\n */\nexport function getUserInfo() {\n  return http.get<UserInfo>('/profile/me');\n}\n\n/**\n * 上传用户头像\n */\nexport function uploadAvatar(file: File) {\n  return http.upload<string>('/user/upload-avatar', { file });\n}\n"], "names": ["http"], "mappings": ";;AAUO,SAAS,QAAQ,QAAqB;AACpC,SAAAA,uBAAK,KAAkB,sBAAsB,MAAM;AAC5D;AAKO,SAAS,kBAAkB;AACzB,SAAAA,kBAAA,KAAK,IAAmB,eAAe;AAChD;AAKO,SAAS,eAAe;AACtB,SAAAA,kBAAA,KAAK,IAAgB,YAAY;AAC1C;AAKO,SAAS,eAAe,QAAwB;AAC9C,SAAAA,uBAAK,KAAc,YAAY,MAAM;AAC9C;;;;;"}